#!/usr/bin/env python3
"""
Analyze the adjusted macro data to understand the actual state after alignment
"""

import sys
sys.path.append('/Users/<USER>/Documents/GitHub/macroeconomic-framework/src')

from data_handler import YemenDataHandler
from target_processor import IMFTargetProcessor
from identity_validator import IdentityValidator
import pandas as pd
import numpy as np

def main():
    print("="*80)
    print("ADJUSTED DATA ANALYSIS: Post-Alignment State")
    print("="*80)
    
    # Initialize components with adjusted data
    adjusted_data_handler = YemenDataHandler('/Users/<USER>/Documents/GitHub/macroeconomic-framework/outputs/adjusted_macro_data.csv')
    adjusted_data_handler.load_data()
    
    # Also load original data for comparison
    original_data_handler = YemenDataHandler('/Users/<USER>/Documents/GitHub/macroeconomic-framework/data/yemen_macro_data.csv')
    original_data_handler.load_data()
    
    target_processor = IMFTargetProcessor('/Users/<USER>/Documents/GitHub/macroeconomic-framework/config/imf_targets.yaml', 
                                          '/Users/<USER>/Documents/GitHub/macroeconomic-framework/config/adjustment_rules.yaml')
    
    # Validator using adjusted data
    validator = IdentityValidator(adjusted_data_handler)
    
    print("\n1. ANALYZING 2023 GDP IN ADJUSTED DATA")
    print("-" * 50)
    
    year = 2023
    # Get current GDP and target from adjusted data
    adjusted_gdp = adjusted_data_handler.get_variable('YEMNYGDPMKTPCN', [year])[year]
    original_gdp = original_data_handler.get_variable('YEMNYGDPMKTPCN', [year])[year]
    
    exchange_rate = adjusted_data_handler.get_variable('YEMPANUSATLS', [year])[year]
    gdp_targets = target_processor.get_gdp_targets([year])
    gdp_target_lcu = gdp_targets[year] * 1000 * exchange_rate
    
    adjusted_achievement_rate = adjusted_gdp / gdp_target_lcu
    original_achievement_rate = original_gdp / gdp_target_lcu
    
    print(f"Original GDP (2023): {original_gdp:,.0f} LCU")
    print(f"Adjusted GDP (2023): {adjusted_gdp:,.0f} LCU")
    print(f"Target GDP (2023): {gdp_target_lcu:,.0f} LCU")
    print(f"Exchange Rate: {exchange_rate:.2f}")
    print(f"GDP Target (USD billions): {gdp_targets[year]:.2f}")
    print(f"Original Achievement Rate: {original_achievement_rate*100:.1f}%")
    print(f"Adjusted Achievement Rate: {adjusted_achievement_rate*100:.1f}%")
    print(f"Change in GDP: {adjusted_gdp - original_gdp:,.0f} LCU")
    print(f"Change in Achievement: {(adjusted_achievement_rate - original_achievement_rate)*100:.1f} percentage points")
    
    # Check GDP components in adjusted data
    print(f"\nAdjusted GDP Components Analysis (2023):")
    c_priv = adjusted_data_handler.get_variable('YEMNECONPRVTCN', [year])[year]
    c_gov = adjusted_data_handler.get_variable('YEMNECONGOVTCN', [year])[year]
    invest_total = adjusted_data_handler.get_variable('YEMNEGDIFTOTCN', [year])[year]
    invest_priv = adjusted_data_handler.get_variable('YEMNEGDIFPRVCN', [year])[year]
    invest_gov = adjusted_data_handler.get_variable('YEMNEGDIFGOVCN', [year])[year]
    inventory = adjusted_data_handler.get_variable('YEMNEGDISTKBCN', [year])[year]
    exports = adjusted_data_handler.get_variable('YEMNEEXPGNFSCN', [year])[year]
    imports = adjusted_data_handler.get_variable('YEMNEIMPGNFSCN', [year])[year]
    
    gdp_calc = c_priv + c_gov + invest_total + inventory + (exports - imports)
    
    print(f"  Private Consumption: {c_priv:,.0f}")
    print(f"  Government Consumption: {c_gov:,.0f}")
    print(f"  Total Investment: {invest_total:,.0f}")
    print(f"    - Private Investment: {invest_priv:,.0f}")
    print(f"    - Government Investment: {invest_gov:,.0f}")
    print(f"  Inventory Changes: {inventory:,.0f}")
    print(f"  Exports: {exports:,.0f}")
    print(f"  Imports: {imports:,.0f}")
    print(f"  Net Exports: {exports - imports:,.0f}")
    print(f"  Calculated GDP: {gdp_calc:,.0f}")
    print(f"  Reported GDP: {adjusted_gdp:,.0f}")
    print(f"  GDP Calculation Error: {adjusted_gdp - gdp_calc:,.0f}")
    
    print("\n2. ANALYZING GDP-FISCAL CONSISTENCY IN ADJUSTED DATA")
    print("-" * 50)
    
    # Check fiscal consistency components in adjusted data
    gov_cons_gdp = adjusted_data_handler.get_variable('YEMNECONGOVTCN', [year])[year]
    
    # Try to get fiscal components
    try:
        wages = adjusted_data_handler.get_variable('YEMGGEXPWAGECN', [year])[year]
        goods_services = adjusted_data_handler.get_variable('YEMGGEXPGNFSCN', [year])[year]
        gov_cons_fiscal = wages + goods_services
        
        error_pct = abs((gov_cons_gdp - gov_cons_fiscal) / gov_cons_gdp) * 100
        
        print(f"Government Consumption (GDP accounts): {gov_cons_gdp:,.0f}")
        print(f"Wages (Fiscal accounts): {wages:,.0f}")
        print(f"Goods & Services (Fiscal accounts): {goods_services:,.0f}")
        print(f"Total Fiscal Consumption: {gov_cons_fiscal:,.0f}")
        print(f"Difference: {gov_cons_gdp - gov_cons_fiscal:,.0f}")
        print(f"Error Percentage: {error_pct:.2f}%")
        print(f"Tolerance: 5.0%")
        print(f"Passes Check: {'YES' if error_pct <= 5.0 else 'NO'}")
        
    except Exception as e:
        print(f"Error accessing fiscal variables: {e}")
    
    print("\n3. RUNNING FULL IDENTITY VALIDATION ON ADJUSTED DATA")
    print("-" * 50)
    
    # Run full validation on adjusted data
    validation_results = validator.validate_all([2022, 2023, 2024, 2025], critical_only=False)
    
    print("Identity Validation Results:")
    for identity_name, is_valid in validation_results.items():
        status = "✅ PASS" if is_valid else "❌ FAIL"
        print(f"  {identity_name}: {status}")
    
    # Count passing vs failing
    passing = sum(1 for v in validation_results.values() if v)
    total = len(validation_results)
    print(f"\nSummary: {passing}/{total} identities passing")
    
    print("\n4. ANALYZING WHY FINAL ENFORCEMENT DIDN'T TRIGGER")
    print("-" * 50)
    
    print("Final GDP Target Enforcement Analysis:")
    print(f"  Target threshold for enforcement: 95%")
    print(f"  Current achievement rate: {adjusted_achievement_rate*100:.1f}%")
    
    if adjusted_achievement_rate >= 0.95:
        print(f"  ⚠️  ISSUE IDENTIFIED: Achievement rate ({adjusted_achievement_rate*100:.1f}%) is above 95% threshold")
        print(f"  ⚠️  The _enforce_gdp_target_final method only triggers when achievement < 95%")
        print(f"  ⚠️  This is why GDP is stuck at {adjusted_achievement_rate*100:.1f}% instead of 100%")
        
        excess_over_target = adjusted_gdp - gdp_target_lcu
        print(f"\n  SOLUTION NEEDED:")
        print(f"    - Current GDP excess over target: {excess_over_target:,.0f} LCU")
        print(f"    - Need to REDUCE GDP by: {excess_over_target:,.0f} LCU to reach 100%")
        print(f"    - This can be done by reducing private investment by the same amount")
        print(f"    - Current private investment: {invest_priv:,.0f}")
        print(f"    - New private investment would be: {invest_priv - excess_over_target:,.0f}")
        
        if invest_priv - excess_over_target > 0:
            print(f"    ✅ This adjustment is feasible (private investment remains positive)")
        else:
            print(f"    ❌ This adjustment would make private investment negative!")
    else:
        print(f"  ✅ Achievement rate is below 95%, enforcement should have triggered")
    
    print("\n" + "="*80)
    print("ADJUSTED DATA ANALYSIS COMPLETE")
    print("="*80)

if __name__ == "__main__":
    main()