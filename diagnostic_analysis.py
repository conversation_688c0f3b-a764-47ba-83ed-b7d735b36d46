#!/usr/bin/env python3
"""
Diagnostic Analysis for Yemen IMF Alignment Issues
- Analyze why 2023 GDP is at 109.7% instead of 100%
- Investigate gdp_fiscal_consistency identity failure
"""

import sys
sys.path.append('/Users/<USER>/Documents/GitHub/macroeconomic-framework/src')

from data_handler import YemenDataHandler
from target_processor import IMFTargetProcessor
from identity_validator import IdentityValidator
import pandas as pd
import numpy as np

def main():
    print("="*80)
    print("DIAGNOSTIC ANALYSIS: Yemen IMF Alignment Issues")
    print("="*80)
    
    # Initialize components
    data_handler = YemenDataHandler('/Users/<USER>/Documents/GitHub/macroeconomic-framework/data/yemen_macro_data.csv')
    data_handler.load_data()
    target_processor = IMFTargetProcessor('/Users/<USER>/Documents/GitHub/macroeconomic-framework/config/imf_targets.yaml', 
                                          '/Users/<USER>/Documents/GitHub/macroeconomic-framework/config/adjustment_rules.yaml')
    validator = IdentityValidator(data_handler)
    
    print("\n1. ANALYZING 2023 GDP TARGET ACHIEVEMENT")
    print("-" * 50)
    
    year = 2023
    # Get current GDP and target
    current_gdp = data_handler.get_variable('YEMNYGDPMKTPCN', [year])[year]
    exchange_rate = data_handler.get_variable('YEMPANUSATLS', [year])[year]
    gdp_targets = target_processor.get_gdp_targets([year])
    gdp_target_lcu = gdp_targets[year] * 1000 * exchange_rate
    
    achievement_rate = current_gdp / gdp_target_lcu
    
    print(f"Current GDP (2023): {current_gdp:,.0f} LCU")
    print(f"Target GDP (2023): {gdp_target_lcu:,.0f} LCU")
    print(f"Exchange Rate: {exchange_rate:.2f}")
    print(f"GDP Target (USD billions): {gdp_targets[year]:.2f}")
    print(f"Achievement Rate: {achievement_rate*100:.1f}%")
    print(f"Excess over target: {current_gdp - gdp_target_lcu:,.0f} LCU")
    
    # Check GDP components
    print(f"\nGDP Components Analysis (2023):")
    c_priv = data_handler.get_variable('YEMNECONPRVTCN', [year])[year]
    c_gov = data_handler.get_variable('YEMNECONGOVTCN', [year])[year]
    invest_total = data_handler.get_variable('YEMNEGDIFTOTCN', [year])[year]
    invest_priv = data_handler.get_variable('YEMNEGDIFPRVCN', [year])[year]
    invest_gov = data_handler.get_variable('YEMNEGDIFGOVCN', [year])[year]
    inventory = data_handler.get_variable('YEMNEGDISTKBCN', [year])[year]
    exports = data_handler.get_variable('YEMNEEXPGNFSCN', [year])[year]
    imports = data_handler.get_variable('YEMNEIMPGNFSCN', [year])[year]
    
    gdp_calc = c_priv + c_gov + invest_total + inventory + (exports - imports)
    
    print(f"  Private Consumption: {c_priv:,.0f}")
    print(f"  Government Consumption: {c_gov:,.0f}")
    print(f"  Total Investment: {invest_total:,.0f}")
    print(f"    - Private Investment: {invest_priv:,.0f}")
    print(f"    - Government Investment: {invest_gov:,.0f}")
    print(f"  Inventory Changes: {inventory:,.0f}")
    print(f"  Exports: {exports:,.0f}")
    print(f"  Imports: {imports:,.0f}")
    print(f"  Net Exports: {exports - imports:,.0f}")
    print(f"  Calculated GDP: {gdp_calc:,.0f}")
    print(f"  Reported GDP: {current_gdp:,.0f}")
    print(f"  GDP Calculation Error: {current_gdp - gdp_calc:,.0f}")
    
    print("\n2. ANALYZING GDP-FISCAL CONSISTENCY FAILURE")
    print("-" * 50)
    
    # Check fiscal consistency components
    gov_cons_gdp = data_handler.get_variable('YEMNECONGOVTCN', [year])[year]
    
    # Try to get fiscal components
    try:
        wages = data_handler.get_variable('YEMGGEXPWAGECN', [year])[year]
        goods_services = data_handler.get_variable('YEMGGEXPGNFSCN', [year])[year]
        gov_cons_fiscal = wages + goods_services
        
        error_pct = abs((gov_cons_gdp - gov_cons_fiscal) / gov_cons_gdp) * 100
        
        print(f"Government Consumption (GDP accounts): {gov_cons_gdp:,.0f}")
        print(f"Wages (Fiscal accounts): {wages:,.0f}")
        print(f"Goods & Services (Fiscal accounts): {goods_services:,.0f}")
        print(f"Total Fiscal Consumption: {gov_cons_fiscal:,.0f}")
        print(f"Difference: {gov_cons_gdp - gov_cons_fiscal:,.0f}")
        print(f"Error Percentage: {error_pct:.2f}%")
        print(f"Tolerance: 5.0%")
        print(f"Passes Check: {'YES' if error_pct <= 5.0 else 'NO'}")
        
    except Exception as e:
        print(f"Error accessing fiscal variables: {e}")
        print("Checking available fiscal variables...")
        
        # Check what fiscal variables are available
        all_vars = data_handler.get_all_variables()
        fiscal_vars = [var for var in all_vars if 'FISCAL' in var.upper() or 'YEMGG' in var]
        print(f"Available fiscal variables: {fiscal_vars}")
    
    print("\n3. STRATEGY RECOMMENDATIONS")
    print("-" * 50)
    
    # Calculate what adjustment is needed
    gdp_excess = current_gdp - gdp_target_lcu
    print(f"To reach 100% target achievement:")
    print(f"  - Need to REDUCE GDP by: {gdp_excess:,.0f} LCU")
    print(f"  - This represents: {(gdp_excess/current_gdp)*100:.2f}% reduction")
    
    # Check if private investment can absorb this reduction
    print(f"\nPrivate Investment Analysis:")
    print(f"  - Current Private Investment: {invest_priv:,.0f}")
    print(f"  - After GDP adjustment: {invest_priv - gdp_excess:,.0f}")
    print(f"  - Reduction: {(gdp_excess/invest_priv)*100:.2f}%")
    
    if invest_priv - gdp_excess > 0:
        print("  → Private investment can absorb the full adjustment")
    else:
        print("  → WARNING: Adjustment would make private investment negative!")
        print("  → Need alternative approach or component rebalancing")
    
    print("\n4. ENFORCEMENT THRESHOLD ANALYSIS")
    print("-" * 50)
    print("Current enforcement logic:")
    print(f"  - Only enforces when achievement < 95%")
    print(f"  - Current achievement: {achievement_rate*100:.1f}%")
    print(f"  - Recommendation: Modify enforcement to target 100% ± tolerance")
    
    print("\n" + "="*80)
    print("ANALYSIS COMPLETE")
    print("="*80)

if __name__ == "__main__":
    main()